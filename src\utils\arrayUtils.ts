/**
 * T<PERSON>h tổng các số trong một mảng
 * @param numbers Mảng các số cần tính tổng
 * @returns Tổng của các số trong mảng
 */
export const sumArray = (numbers: number[]): number => {
  return numbers.reduce((sum, current) => sum + current, 0);
};

/**
 * Cập nhật một mảng với các object từ mảng khác và trả về một mảng mới.
 * Sử dụng Map để tối ưu hiệu suất tra cứu.
 *
 * @param {Array<Object>} originalArray Mảng gốc cần được cập nhật.
 * @param {Array<Object>} updatesArray Mảng chứa các object đã được cập nhật.
 * @returns {Array<Object>} Một mảng mới đã được cập nhật.
 */
export const updateArrayWithObjects = (
  originalArray: any,
  updatesArray: any,
) => {
  const updatesMap = new Map(updatesArray.map((obj: any) => [obj.Id, obj]));

  const newArray = originalArray.map((originalObj: any) => {
    return updatesMap.get(originalObj.Id) || originalObj;
  });

  return newArray;
};
