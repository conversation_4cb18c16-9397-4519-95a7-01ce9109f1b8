import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Pressable,
  Modal,
  Alert,
} from 'react-native';
import { FlatList, ScrollView } from 'react-native-gesture-handler';
import {
  FLoading,
  Winicon,
  FDialog,
  showDialog,
  ComponentStatus,
} from 'wini-mobile-components';
import { Title } from '../../../Config/Contanst';
import { OrderData } from '../../../mock/shopData';
import ConfigAPI from '../../../Config/ConfigAPI';
import { TypoSkin } from '../../../assets/skin/typography';
import { ColorThemes } from '../../../assets/skin/colors';
import { Ultis } from '../../../utils/Utils';

const CardOrder = ({
  item,
  index,
  action,
  handleUpdateStatusProcessOrder,
}: {
  item: any;
  index: number;
  action?: string;
  handleUpdateStatusProcessOrder: (item: any, type?: string) => void;
}) => {
  const [showStatusPopup, setShowStatusPopup] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [showAllProducts, setShowAllProducts] = useState(false);
  const dialogRef = useRef<any>(null);

  // Danh sách các trạng thái có thể chọn
  const statusOptions = [
    { id: 1, name: 'Đang xử lý', type: 'processing' },
    { id: 3, name: 'Hoàn thành', type: 'completed' },
    { id: 4, name: 'Hủy', type: 'cancelled' },
  ];

  // Lọc trạng thái dựa trên trạng thái hiện tại của item
  const getFilteredStatusOptions = () => {
    if (item?.Status === 1) {
      return statusOptions.filter(status => status.type === 'processing');
    } else if (item?.Status === 2) {
      return statusOptions.filter(status => status.type === 'completed');
    }
    return statusOptions;
  };

  const handleStatusPress = () => {
    setSelectedStatus(null); // Reset selection when opening modal
    setShowStatusPopup(true);
  };

  const handleCancelPress = () => {
    showDialog({
      ref: dialogRef,
      status: ComponentStatus.WARNING,
      title: 'Xác nhận hủy đơn hàng',
      content: `Bạn có chắc chắn muốn hủy đơn hàng #${item?.Code}?`,
      onSubmit: async () => {
        handleUpdateStatusProcessOrder(item, 'cancelled');
      },
    });
  };

  const handleStatusSelect = (statusType: string) => {
    setSelectedStatus(statusType);
  };

  const handleUpdateStatus = async () => {
    if (selectedStatus) {
      await handleUpdateStatusProcessOrder(item, selectedStatus);
      setShowStatusPopup(false);
      setSelectedStatus(null);
    }
  };

  const handleToggleProducts = () => {
    setShowAllProducts(!showAllProducts);
  };

  // Lấy danh sách sản phẩm để hiển thị
  const getDisplayProducts = () => {
    if (!item?.orderDetails || item.orderDetails.length === 0) return [];
    return showAllProducts ? item.orderDetails : [item.orderDetails[0]];
  };

  return (
    <Pressable key={`key ${index}`}>
      <FDialog ref={dialogRef} />
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={{ ...styles.orderId, gap: 2, flexDirection: 'row' }}>{
            'Đơn hàng '}
            <Text style={{ color: '#000', fontWeight: 'bold' }}>
              #{item?.Code}
            </Text>
          </Text>
          <Text
            style={
              item?.Status == 3 ? styles.statusDone : item?.Status == 2 || item?.Status == 1 ? styles.statusProcessing : styles.status
            }>
            {item?.Status == 1 && 'Đang thực hiện'}
            {item?.Status == 2 && 'Đang thực hiện'}
            {item?.Status == 3 && 'Hoàn thành'}
            {item?.Status == 4 && 'Hủy'}
          </Text>
        </View>
        {/* Thông tin sản phẩm */}
        {getDisplayProducts().map((productItem: any, index: number) => (
          <View style={styles.productContainer} key={`${index}-${productItem?.Id}`}>
            <Image
              source={{ uri: ConfigAPI.urlImg + productItem?.productInfo?.Img }}
              style={styles.productImage}
            />
            <View style={styles.productInfo}>
              <Text style={styles.productName}>{productItem?.productInfo?.Name}</Text>
              <Text style={styles.productDetails}>
                {
                  (<>
                    <Text style={styles.productName}>Hoàn tiền :</Text>
                    <Text style={styles.productDetails}>
                      <Text>
                        (kh:{' '}
                        {Ultis.money(JSON.parse(productItem?.Refund ?? '{}')?.f0 ?? 0)}
                        đ)-(f1:
                        {Ultis.money(JSON.parse(productItem?.Refund ?? '{}')?.f1 ?? 0)}
                        đ)-(f2:
                        {Ultis.money(JSON.parse(productItem?.Refund ?? '{}')?.f2 ?? 0)}
                        đ)
                      </Text>
                    </Text> </>
                  )
                }
              </Text>
              <Text style={styles.productPrice}>
                <Text style={styles.productName}>Giá:</Text>
                <Text style={{ color: ColorThemes.light.error_main_color }}>
                  {Ultis.money(productItem?.productInfo?.Price)} VNĐ
                </Text>
              </Text>
            </View>
          </View>
        ))}


        {/* Số lượng và tổng tiền */}
        <View style={styles.quantityTotal}>
          {item?.orderDetails?.length > 1 && (
            <TouchableOpacity
              style={styles.quantityButton}
              onPress={handleToggleProducts}
            >
              <Text style={styles.quantityText}>
                <Text
                  style={{
                    ...TypoSkin.title3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  {showAllProducts ? 'Thu gọn' : 'Xem thêm'}
                </Text>
                <Winicon
                  src={showAllProducts ? "color/arrows/arrow-sm-up" : "color/arrows/arrow-sm-down"}
                  size={13}
                  color={ColorThemes.light.neutral_text_title_color}
                />
              </Text>
            </TouchableOpacity>
          )}
          <View style={styles.quantityDetail}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Text style={styles.quantity}>Tổng hoàn ({item?.orderDetails?.length ?? 0} sản phẩm):</Text>
              <Text style={styles.money}>{!item?.Refund ? 0 : item.orderDetails?.reduce((total: number, orderDetail: any) => total + (JSON.parse(orderDetail?.Refund ?? '{}')?.f0 ?? 0) + (JSON.parse(orderDetail?.Refund ?? '{}')?.f1 ?? 0) + (JSON.parse(orderDetail?.Refund ?? '{}')?.f2 ?? 0), 0)} VNĐ</Text>
            </View>
          </View>
          <View style={styles.quantityDetail}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Text style={styles.quantity}>
                Tổng tiền ({item?.orderDetails?.length ?? 0} sản phẩm):
              </Text>
              <Text style={styles.money}>{Ultis.money(item?.Value)} VNĐ</Text>
            </View>
          </View>
        </View>
        {action && action !== Title.Cancel && item?.Status !== 3 && (
          <View style={styles.button}>
            <View style={{ flexDirection: 'row', gap: 10 }}></View>
            <View style={{ flexDirection: 'row', gap: 10 }}>
              <TouchableOpacity
                style={styles.confirmButton}
                onPress={handleStatusPress}>
                <Text style={styles.confirmButtonText}>
                  Cập nhật trạng thái
                </Text>
              </TouchableOpacity>
              {item?.Status == 2 && (
                <TouchableOpacity
                  style={styles.confirmButton}
                  onPress={handleCancelPress}>
                  <Text style={styles.confirmButtonText}>Xác nhận hủy</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        )}
      </View>
      <Modal
        visible={showStatusPopup}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowStatusPopup(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {/* Header */}
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                Cập nhật trạng thái đơn hàng
              </Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowStatusPopup(false)}>
                <Text style={styles.closeButtonText}>×</Text>
              </TouchableOpacity>
            </View>

            {/* Order Info */}
            <View style={styles.orderInfoContainer}>
              <Text style={styles.orderInfoText}>Đơn hàng #{item?.Id}</Text>
            </View>
            <View style={styles.statusList}>
              {getFilteredStatusOptions().map(status => (
                <TouchableOpacity
                  key={status.id}
                  style={[
                    styles.statusOption,
                    selectedStatus === status.type && styles.selectedStatus,
                  ]}
                  onPress={() => handleStatusSelect(status.type)}>
                  <View style={styles.statusOptionContent}>
                    <View
                      style={[
                        styles.statusIndicator,
                        selectedStatus === status.type &&
                        styles.selectedIndicator,
                      ]}
                    />
                    <Text
                      style={[
                        styles.statusOptionText,
                        selectedStatus === status.type &&
                        styles.selectedStatusText,
                      ]}>
                      {status.name}
                    </Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>

            {/* Action Buttons */}
            <View style={styles.actionButtonsContainer}>
              <View style={styles.additionalButtons}>
                <TouchableOpacity
                  style={[
                    styles.additionalButton,
                    !selectedStatus && styles.disabledButton,
                  ]}
                  onPress={() => handleUpdateStatus()}
                  disabled={!selectedStatus}>
                  <Text
                    style={[
                      styles.additionalButtonText,
                      !selectedStatus && styles.disabledButtonText,
                    ]}>
                    Cập nhật trạng thái
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#ddd',
    padding: 10,
    margin: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2, // Bóng cho Android
    marginTop: 6,
  },
  header: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  orderId: {
    flex: 0.6,
    ...TypoSkin.title2,
    fontWeight: '500',
    color: ColorThemes.light.neutral_text_title_color,
    fontSize: 14,
  },
  status: {
    flex: 0.4,
    textAlign: 'right',
    ...TypoSkin.title3,
    color: '#DA251D',
    fontSize: 14,
  },
  statusProcessing: {
    flex: 0.4,
    textAlign: 'right',
    ...TypoSkin.title3,
    color: '#FA8C16',
    fontSize: 14,
  },
  statusDone: {
    flex: 0.3,
    textAlign: 'right',
    ...TypoSkin.title3,
    color: ColorThemes.light.success_main_color,
    fontSize: 14,
  },
  productContainer: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  productImage: {
    borderWidth: 5,
    borderRadius: 50,
    width: 60,
    height: 60,
    marginRight: 16,
    borderColor: '#F8F8FF',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 2,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    ...TypoSkin.title4,
    fontWeight: '500',
    color: ColorThemes.light.neutral_text_title_color,
  },
  productDetails: {
    ...TypoSkin.title4,
    color: ColorThemes.light.neutral_text_title_color,
    marginVertical: 2,
  },
  productPrice: {
    ...TypoSkin.title4,
    color: ColorThemes.light.neutral_text_title_color,
  },
  quantityTotal: {
    flexDirection: 'column',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    marginBottom: 10,
  },
  quantityButton: {
    borderRadius: 5,
    paddingHorizontal: 10,
    paddingVertical: 5,
  },
  quantityText: {
    fontSize: 14,
    color: '#555',
    display: 'flex',
    gap: 1,
  },
  quantityDetail: {
    height: 30,
    width: '100%',
    alignItems: 'flex-end',
  },
  quantity: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    display: 'flex',
    justifyContent: 'flex-end',
  },
  money: {
    ...TypoSkin.title3,
    color: '#DA251D',
    fontWeight: '700',
    fontSize: 14,
  },
  button: {
    minHeight: 40,
    width: '100%',
    display: 'flex',
    alignItems: 'flex-end',
  },
  confirmButton: {
    backgroundColor: '#FFC043',
    width: 154,
    borderRadius: 50,
    paddingVertical: 10,
    alignItems: 'center',
  },
  confirmButtonText: {
    ...TypoSkin.title3,
    color: '#DA251D',
    fontWeight: 'bold',
    fontSize: 14,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 15,
    width: '85%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  modalTitle: {
    ...TypoSkin.title2,
    fontWeight: 'bold',
    color: ColorThemes.light.neutral_text_title_color,
    flex: 1,
  },
  modalMessage: {
    ...TypoSkin.title4,
    color: ColorThemes.light.neutral_text_title_color,
    marginTop: 5,
    lineHeight: 20,
  },
  statusList: {
    width: '100%',
    marginBottom: 25,
  },
  statusOption: {
    paddingVertical: 15,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 12,
    marginBottom: 10,
    backgroundColor: '#fafafa',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedStatus: {
    backgroundColor: '#ffffff',
    borderColor: '#FFD700',
    borderWidth: 2,
  },
  statusOptionText: {
    ...TypoSkin.title4,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: '500',
  },
  selectedStatusText: {
    color: '#000000',
    fontWeight: 'bold',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalButton: {
    marginHorizontal: 5,
  },
  cancelButton: {
    backgroundColor: '#FFA500',
    padding: 10,
    borderRadius: 50,
    flex: 1,
    alignItems: 'center',
  },
  cancelButtonText: {
    ...TypoSkin.title3,
    color: ColorThemes.light.error_main_color,
    fontWeight: 'bold',
  },
  confirmModalButton: {
    backgroundColor: '#FFA500',
    padding: 10,
    borderRadius: 50,
    flex: 1,
    alignItems: 'center',
  },
  confirmModalButtonText: {
    ...TypoSkin.title3,
    color: ColorThemes.light.error_main_color,
    fontWeight: 'bold',
  },
  additionalButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 10,
  },
  additionalButton: {
    backgroundColor: '#FFD700',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    alignItems: 'center',
  },
  additionalButtonText: {
    ...TypoSkin.title4,
    color: '#FF0000',
    fontWeight: '500',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  closeButtonText: {
    ...TypoSkin.title2,
    color: '#666',
    fontWeight: 'bold',
    lineHeight: 20,
    textAlign: 'center',
  },
  orderInfoContainer: {
    marginBottom: 20,
    alignItems: 'center',
  },
  orderInfoText: {
    ...TypoSkin.title2,
    fontWeight: 'bold',
    color: ColorThemes.light.neutral_text_title_color,
  },
  sectionTitle: {
    ...TypoSkin.title2,
    fontWeight: 'bold',
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 10,
  },
  statusOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginRight: 10,
  },
  selectedIndicator: {
    backgroundColor: '#FFD700',
    borderWidth: 2,
    borderColor: '#FFD700',
  },
  actionButtonsContainer: {
    marginBottom: 20,
  },
  disabledButton: {
    backgroundColor: '#e0e0e0',
    borderColor: '#ccc',
  },
  disabledButtonText: {
    color: '#999',
  },
});

export default CardOrder;
