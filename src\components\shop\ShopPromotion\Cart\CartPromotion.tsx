import { Text } from 'react-native-paper';
import { Checkbox } from 'wini-mobile-components';
import { TypoSkin } from '../../../../assets/skin/typography';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { faAngleRight } from '@fortawesome/free-solid-svg-icons';
import { ColorThemes } from '../../../../assets/skin/colors';
import { Image, TouchableOpacity, View } from 'react-native';
import ConfigAPI from '../../../../Config/ConfigAPI';

export default function CartPromotion({
  item,
  index,
  handleSelectCategory,
  handleSelectAllCategory,
  selectTwo,
  getdataByMenuTwo,
  selectThree,
  handleGetDataDiscount,
  selectAll,
  selectedItems,
  isSelected,
  handleSelectChild,
}: {
  item: any;
  index: number;
  handleSelectCategory: (item: any) => void;
  handleSelectAllCategory: (item: any) => void;
  selectTwo: boolean;
  getdataByMenuTwo: (item: any) => void;
  selectThree: boolean;
  handleGetDataDiscount: (data: any) => void;
  selectAll: boolean;
  selectedItems: Set<string>;
  isSelected: boolean;
  handleSelectChild?: (child: any) => void;
}) {
  // Kiểm tra xem danh mục này có children không
  const hasChildren = item?.Children && item?.Children.length > 0;

  // Kiểm tra xem tất cả children đã được chọn chưa
  const allChildrenSelected = hasChildren
    ? item.Children.every((child: any) => selectedItems.has(child.Id))
    : false;

  return (
    <View
      key={`title-${index}`}
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingBottom: 10,
        paddingTop: 10,
        borderBottomWidth: 1,
        borderBottomColor: ColorThemes.light.neutral_main_background_color,
        marginLeft: 16,
      }}>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginLeft: 15,
        }}>
        {selectTwo && !selectThree ? (
          <Checkbox
            value={isSelected}
            onChange={() => {
              handleGetDataDiscount(item);
            }}
            size={24}
          />
        ) : (
          <Checkbox
            value={selectThree ? selectThree : isSelected}
            onChange={() => {
              handleGetDataDiscount(item);
            }}
            size={24}
          />
        )}

        <View>
          <Image
            source={{ uri: ConfigAPI.urlImg + item?.Img }}
            style={{ height: 46, width: 46, borderRadius: 50, marginLeft: 15 }}
          />
        </View>
        <Text style={{ marginLeft: 14, ...TypoSkin.body2 }}>{item?.Name}</Text>
      </View>
      {selectAll ?
        <View>

        </View>
        :
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>


          {!selectTwo && !selectThree && (
            <TouchableOpacity
              style={{ marginRight: 16 }}
              onPress={() => handleSelectCategory(item)}>
              <FontAwesomeIcon
                icon={faAngleRight}
                color={ColorThemes.light.black}
                size={16}
              />
            </TouchableOpacity>
          )}
          {selectTwo && !selectThree && (
            <TouchableOpacity
              style={{ marginRight: 16 }}
              onPress={() => getdataByMenuTwo(item)}>
              if(selectDataTwo){ }
              <FontAwesomeIcon
                icon={faAngleRight}
                color={ColorThemes.light.black}
                size={16}
              />
            </TouchableOpacity>
          )}
        </View>
      }

    </View >
  );
}
