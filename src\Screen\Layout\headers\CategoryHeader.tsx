import React, {useEffect, useCallback, memo, useRef, useState} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Platform,
  Image,
  Text,
} from 'react-native';
import {AppSvg, FDialog} from 'wini-mobile-components';
import {navigate, RootScreen} from '../../../router/router';
import CartIcon from '../../../components/CartIcon';
import iconSvg from '../../../svg/icon';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import HeaderBackground from '../../../components/shop/HeaderShop';
import {dialogCheckAcc} from '../mainLayout';
import {useCategoryHook} from '../../../redux/hook/categoryHook';
import {ColorThemes} from '../../../assets/skin/colors';
import SearchBar from '../../../components/shop/Search';
import {useSelector} from 'react-redux';
import {RootState} from '../../../redux/store/store';
import {useProductByCategoryHook} from '../../../redux/reducers/ProductByCategoryReducer';
import PopupFilterProduct from '../../../modules/Product/Popup/PopupFilterProduct';
import {useNavigation} from '@react-navigation/native';

const HeaderIconButton = memo(
  ({onPress, children}: {onPress?: () => void; children: React.ReactNode}) => (
    <TouchableOpacity style={styles.iconButton} onPress={onPress}>
      <View style={styles.iconCircle}>{children}</View>
    </TouchableOpacity>
  ),
);

const RightHeaderActions = memo(
  ({
    onNotificationPress,
    onCartPress,
  }: {
    onNotificationPress: () => void;
    onCartPress: () => void;
  }) => (
    <View style={styles.rightIcons}>
      <Image
        source={require('../../../assets/images/logo.png')}
        style={{width: 30, height: 30, borderRadius: 20}}
      />
      <HeaderIconButton onPress={onNotificationPress}>
        <AppSvg SvgSrc={iconSvg.notification} size={16} />
      </HeaderIconButton>
      <HeaderIconButton onPress={onCartPress}>
        <CartIcon isHome color="#0033CC" size={18} showBadge={true} />
      </HeaderIconButton>
    </View>
  ),
);

const CategoryHeader = () => {
  const navigation = useNavigation();
  const categoryHook = useCategoryHook();
  const productByCategoryHook = useProductByCategoryHook();
  const customer = useSelectorCustomerState().data;
  const dialogRef = React.useRef<any>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const [showFilter, setShowFilter] = useState(false);

  const {parentCategory, filter} = useSelector(
    (state: RootState) => state.productByCategory,
  );

  useEffect(() => {
    if (Platform.OS === 'android') {
      StatusBar.setTranslucent(true);
      StatusBar.setBackgroundColor('transparent');
    }
    StatusBar.setBarStyle('dark-content');

    return () => {
      if (Platform.OS === 'android') {
        StatusBar.setTranslucent(false);
        StatusBar.setBackgroundColor('transparent');
      }
    };
  }, []);

  // quay trở về trang trước
  const onBack = () => {
    navigation.goBack();
  };

  // kiểm tra đăng nhập
  const handleProtectedAction = useCallback(
    (action: () => void) => {
      if (!customer?.Id) {
        dialogCheckAcc(dialogRef);
        return;
      }
      action();
    },
    [customer],
  );

  // tìm kiếm sản phẩm
  const onSearch = useCallback(
    (text: string) => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }

      searchTimeoutRef.current = setTimeout(() => {
        productByCategoryHook.setData('filter', {
          ...filter,
          textSearch: text,
        });
      }, 1000);
    },
    [filter],
  );

  // áp dụng lọc sản phẩm
  const onApplyFilter = useCallback(
    (data: any) => {
      productByCategoryHook.setData('filter', {
        ...filter,
        ...data,
      });
      if (data.categoryId) {
        productByCategoryHook.setData('newCategoryId', data.categoryId);
      }
    },
    [filter, productByCategoryHook],
  );

  // xem thông báo
  const handleNotificationPress = () =>
    handleProtectedAction(() => {
      navigate(RootScreen.Notification);
    });

  // xem giỏ hàng
  const handleCartPress = () =>
    handleProtectedAction(() => {
      navigate(RootScreen.CartPage);
    });

  // hiển thị popup lọc sản phẩm
  const onShowFilter = () => {
    setShowFilter(true);
  };

  return (
    <View style={styles.header}>
      <FDialog ref={dialogRef} />
      <View style={styles.headerBackground}>
        <HeaderBackground />
      </View>
      <View style={styles.headerContent}>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <TouchableOpacity onPress={onBack}>
            <AppSvg SvgSrc={iconSvg.arrowLeft} size={16} />
          </TouchableOpacity>
          <Text
            style={{
              fontSize: 16,
              fontWeight: 'bold',
              marginLeft: 10,
              color: ColorThemes.light.infor_text_color,
            }}>
            {parentCategory?.Name || ''}
          </Text>
        </View>
        <RightHeaderActions
          onNotificationPress={handleNotificationPress}
          onCartPress={handleCartPress}
        />
      </View>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        <View style={{width: '90%', zIndex: 20}}>
          <SearchBar setDataSearch={onSearch} />
        </View>
        <TouchableOpacity onPress={onShowFilter} style={{zIndex: 20}}>
          <AppSvg
            SvgSrc={iconSvg.filter}
            color="#000"
            size={25}
            style={{marginRight: 12}}
          />
        </TouchableOpacity>
      </View>
      <PopupFilterProduct
        visible={showFilter}
        onClose={() => setShowFilter(false)}
        onApply={onApplyFilter}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    width: '100%',
    position: 'relative',
    paddingTop: 16,
  },
  headerBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  headerContent: {
    marginTop: 40,
    width: '100%',
    height: 50,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    position: 'relative',
    zIndex: 2,
    paddingHorizontal: 12,
  },
  rightIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    marginLeft: 8,
  },
  iconCircle: {
    width: 32,
    height: 32,
    borderRadius: 20,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
});

export default CategoryHeader;
