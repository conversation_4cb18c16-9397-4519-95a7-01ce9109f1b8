import { getDataToAsyncStorage } from '../utils/AsyncStorage';
import { StorageContanst } from '../Config/Contanst';
import SocketService from '../modules/chat/services/SocketService';
import store from '../redux/store/store';
import { connectSocket, disconnectSocket } from '../redux/reducers/ChatReducer';

/**
 * Service để quản lý kết nối socket khi user đăng nhập/đăng xuất
 */
class AuthSocketService {
  private isInitialized = false;

  /**
   * Khởi tạo kết nối socket khi user đã đăng nhập
   * Được gọi khi:
   * - App khởi động và có token
   * - User đăng nhập thành công
   * - User đăng nhập bằng social
   */
  async initializeSocketConnection(userId?: string) {
    try {
      console.log('🔌 Initializing socket connection...');

      // Lấy thông tin từ AsyncStorage nếu không được truyền vào
      const finalUserId = userId || await this.getUserId();
      if (!finalUserId) {
        console.log('❌ Missing userId  for socket connection');
        return false;
      }

      // Kiểm tra xem đã kết nối chưa
      if (this.isInitialized && SocketService.getConnectionStatus()) {
        console.log('✅ Socket already connected');
        return true;
      }

      // Kết nối socket
      console.log(`🔌 Connecting socket for user: ${finalUserId}`);
      await SocketService.connect(finalUserId);
      
      // Cập nhật Redux state
      store.dispatch(connectSocket(finalUserId));
      
      this.isInitialized = true;
      console.log('✅ Socket connection initialized successfully');
      
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize socket connection:', error);
      this.isInitialized = false;
      return false;
    }
  }

  /**
   * Ngắt kết nối socket khi user đăng xuất
   */
  disconnectSocket() {
    try {
      console.log('🔌 Disconnecting socket...');
      
      SocketService.disconnect();
      store.dispatch(disconnectSocket());
      
      this.isInitialized = false;
      console.log('✅ Socket disconnected successfully');
    } catch (error) {
      console.error('❌ Failed to disconnect socket:', error);
    }
  }

  /**
   * Kiểm tra trạng thái kết nối socket
   */
  isConnected(): boolean {
    return this.isInitialized && SocketService.getConnectionStatus();
  }

  /**
   * Lấy userId từ Redux store hoặc AsyncStorage
   */
  private async getUserId(): Promise<string | null> {
    try {
      // Lấy từ Redux store trước
      const state = store.getState();
      const customer = state.customer?.data;
      
      if (customer?.id || customer?.Id) {
        return customer.id || customer.Id;
      }

      // Fallback: có thể lấy từ AsyncStorage nếu cần
      // const userId = await getDataToAsyncStorage('userId');
      // return userId;

      return null;
    } catch (error) {
      console.error('Error getting userId:', error);
      return null;
    }
  }

  /**
   * Retry kết nối socket nếu bị mất kết nối
   */
  async retryConnection() {
    if (!this.isConnected()) {
      console.log('🔄 Retrying socket connection...');
      return await this.initializeSocketConnection();
    }
    return true;
  }
}

export default new AuthSocketService();
