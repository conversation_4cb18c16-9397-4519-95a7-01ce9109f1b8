import React from 'react';
import {View, TextInput, StyleSheet} from 'react-native';

const SearchBar = ({
  setDataSearch,
}: {
  setDataSearch: (data: string) => void;
}) => {
  return (
    <View style={styles.container}>
      <TextInput
        style={styles.input}
        placeholder="Bạn muốn tìm gì?"
        placeholderTextColor="#999"
        onChange={e => setDataSearch(e.nativeEvent.text)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 11, // Bo góc đểව: '2px solid #ddd',
    paddingHorizontal: 15,
    paddingVertical: 5,
    borderWidth: 0.5,
    borderColor: '#999',
    margin: 10,
  },
  input: {
    flex: 1,
    color: '#333',
    paddingVertical: 0,
  },
});

export default SearchBar;
