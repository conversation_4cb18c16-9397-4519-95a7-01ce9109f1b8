import {createAsyncThunk} from '@reduxjs/toolkit';
import {showSnackbar, ComponentStatus} from 'wini-mobile-components';
import {DataController} from '../../base/baseController';
import {RootState} from '../store/store';
import {Category} from '../models/category';

function handleDataCategories(data: Category[]) {
  const parentCategory = data.filter(i => !i.ParentId);
  parentCategory.forEach(i => {
    const children = data.filter(j => j.ParentId === i.Id);
    i.Children = children;
  });
  return parentCategory;
}

const categoryAction = {
  find: async (query: string) => {
    const controller = new DataController('Category');
    const res = await controller.getListSimple({
      page: 1,
      size: 1000,
      query,
    });
    if (res.code === 200) {
      return res.data;
    }
    return [];
  },
  findOne: async (id: string) => {
    const controller = new DataController('Category');
    const res = await controller.getById(id);
    return res.data;
  },
};

const fetchCategories = createAsyncThunk<
  Category[],
  {page?: number; status?: number} | undefined,
  {state: RootState}
>('category/fetchCategories', async (config, thunkAPI: any) => {
  const controller = new DataController('Category');
  try {
    const res = await controller.aggregateList({
      page: config?.page ?? 1,
      size: 1000,
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
    });
    if (res.code === 200) {
      return handleDataCategories(res.data);
    }
  } catch (err: any) {
    const errorMessage = err.message || 'An unknown error occurred';
    showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

export {fetchCategories, categoryAction};
