import React from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { ColorThemes } from '../../assets/skin/colors';

interface BasePopupConfirmProps {
  visible: boolean;
  title: string;
  message?: string;
  onCancel: () => void;
  onConfirm: () => void;
  cancelText?: string;
  confirmText?: string;
  type?: 'normal' | 'delete';
  loading?: boolean;
}

/**
 * Component hiển thị hộp thoại xác nhận (confirm dialog)
 * @param {BasePopupConfirmProps} props
 * @param {boolean} props.visible - Trạng thái ẩn/hiện của dialog
 * @param {string} props.title - Tiêu đề của dialog
 * @param {string} [props.message] - (Không bắt buộc) Nội dung/thông điệp chi tiết
 * @param {function} props.onCancel - Hàm sẽ được gọi khi nhấn nút "Huỷ" hoặc đóng dialog
 * @param {function} props.onConfirm - Hàm sẽ được gọi khi nhấn nút "Xác nhận"
 * @param {string} [props.cancelText="Huỷ"] - (Không bắt buộc) Text cho nút huỷ
 * @param {string} [props.confirmText="Xác nhận"] - (Không bắt buộc) Text cho nút xác nhận
 * @param {'normal' | 'delete'} [props.type='normal'] - (Không bắt buộc) Loại popup, 'delete' sẽ đổi màu và vị trí nút xác nhận
 */
const BasePopupConfirm: React.FC<BasePopupConfirmProps> = ({
  visible,
  title,
  message,
  onCancel,
  onConfirm,
  cancelText = 'Huỷ',
  confirmText = 'Xác nhận',
  type = 'normal',
  loading = false,
}) => {
  if (!visible) {
    return null;
  }

  const confirmButton = (
    <TouchableOpacity
      disabled={loading}
      style={[
        styles.button,
        type === 'delete' ? styles.deleteButton : styles.confirmButton,
        loading && styles.loadingButton,
      ]}
      onPress={onConfirm}>
      <View style={styles.buttonContent}>
        {loading && (
          <ActivityIndicator
            size="small"
            color="white"
            style={styles.loadingIndicator}
          />
        )}
        <Text style={[styles.buttonText, styles.confirmButtonText]}>
          {confirmText}
        </Text>
      </View>
    </TouchableOpacity>
  );
  const cancelButton = (
    <TouchableOpacity
      style={[styles.button, styles.cancelButton]}
      onPress={onCancel}>
      <Text style={styles.buttonText}>{cancelText}</Text>
    </TouchableOpacity>
  );
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onCancel} // Dành cho nút back trên Android
    >
      <View style={styles.modalOverlay}>
        <View style={styles.dialogContainer}>
          <Text style={styles.title}>{title}</Text>

          {message && <Text style={styles.message}>{message}</Text>}

          <View style={styles.buttonContainer}>
            {type === 'delete' ? (
              <>
                {confirmButton}
                {cancelButton}
              </>
            ) : (
              <>
                {cancelButton}
                {confirmButton}
              </>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Nền mờ
    justifyContent: 'center',
    alignItems: 'center',
  },
  dialogContainer: {
    width: '85%',
    maxWidth: 400,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    // Shadow cho iOS
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    // Shadow cho Android
    elevation: 5,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
    color: '#111',
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
    lineHeight: 22,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  cancelButton: {
    backgroundColor: '#f0f0f0',
  },
  confirmButton: {
    backgroundColor: ColorThemes.light.infor_main_color,
  },
  deleteButton: {
    backgroundColor: '#dc3545',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111',
  },
  confirmButtonText: {
    color: 'white',
  },
  loadingButton: {
    opacity: 0.7,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingIndicator: {
    marginRight: 8,
  },
});

export default BasePopupConfirm;
export type { BasePopupConfirmProps };
