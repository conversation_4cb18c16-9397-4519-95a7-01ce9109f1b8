import { DataController } from '../../base/baseController';

export class ShopDA {
  private ShopController: DataController;
  private OrderController: DataController;
  private PaymentWithdrawController: DataController;

  constructor() {
    this.ShopController = new DataController('Shop');
    this.OrderController = new DataController('Order');
    this.PaymentWithdrawController = new DataController('PaymentWithdraw');
  }

  async getShop(cusId: string) {
    const response = await this.ShopController.aggregateList({
      page: 1,
      size: 1,
      searchRaw: `@CustomerId: {${cusId}}`,
    });
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  /**
   * Lấy thông tin đơn hàng và giao dịch rút tiền theo shopId
   * @param shopId - ID của shop
   * @returns Object chứa tổng tiền đơn hàng, tổng tiền giao dịch và số dư
   */
  async getShopFinancialSummary(shopId: string) {
    try {
      // Bước 1: <PERSON><PERSON><PERSON> tất cả đơn hàng có status = 3 (thành công)
      const orderResponse = await this.OrderController.getPatternList({
        query: `@ShopId: {${shopId}} @Status: [3 3] @PaymentType: [2 2]`,
        pattern: {
          Id: ['Id', 'ShopId', 'Status', 'TotalAmount', 'Value', 'DateCreated'],
        },
      });

      // Bước 2: Lấy tất cả giao dịch rút tiền có status = 1 hoặc 2
      const paymentWithdrawResponse = await this.PaymentWithdrawController.getPatternList({
        query: `@ShopId: {${shopId}} @Status: [1 1 2 2]`,
        pattern: {
          Id: ['Id', 'ShopId', 'Status', 'Amount', 'DateCreated'],
        },
      });

      // Tính tổng tiền đơn hàng
      let totalOrderAmount = 0;
      if (orderResponse?.code === 200 && orderResponse.data) {
        totalOrderAmount = orderResponse.data.reduce((sum: number, order: any) => {
          return sum + (order.TotalAmount || order.Value || 0);
        }, 0);
      }

      // Tính tổng tiền giao dịch rút
      let totalWithdrawAmount = 0;
      if (paymentWithdrawResponse?.code === 200 && paymentWithdrawResponse.data) {
        totalWithdrawAmount = paymentWithdrawResponse.data.reduce((sum: number, payment: any) => {
          return sum + (payment.Amount || 0);
        }, 0);
      }

      // Tính số dư (tổng đơn hàng - tổng rút tiền)
      const balance = totalOrderAmount - totalWithdrawAmount;

      return {
        code: 200,
        data: {
          shopId: shopId,
          totalOrderAmount: totalOrderAmount,
          totalWithdrawAmount: totalWithdrawAmount,
          balance: balance,
          orders: orderResponse?.data || [],
          withdrawals: paymentWithdrawResponse?.data || [],
        },
        message: 'Lấy thông tin tài chính thành công',
      };
    } catch (error) {
      console.error('getShopFinancialSummary error:', error);
      return {
        code: 500,
        data: null,
        message: 'Lỗi khi lấy thông tin tài chính',
      };
    }
  }
}

export default ShopDA;
