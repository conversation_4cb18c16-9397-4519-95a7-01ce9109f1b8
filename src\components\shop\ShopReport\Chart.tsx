import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { LineChart } from 'react-native-chart-kit';
import { ScrollView } from 'react-native-gesture-handler';
import LinearGradient from 'react-native-linear-gradient';
import ChartMoney from './ChartMakeMoney';
import { ChartType } from '../../../Config/Contanst';
import ChartProduct from './ChartProduct';
import ChartOrder from './ChartOrder';
import { ColorThemes } from '../../../assets/skin/colors';

const { width } = Dimensions.get('window');

const Chart = () => {
    const [SelectChart, setSelectChart] = useState(ChartType.MakeMoney);

    return (
        <View style={styles.container}>
            <ScrollView style={styles.tabContainer} horizontal={true}>
                {SelectChart == ChartType.MakeMoney ? (
                    <LinearGradient
                        colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
                        style={styles.tabAction}
                        start={{x: 0, y: 0}}
                        end={{x: 1, y: 0}}
                    >
                        <TouchableOpacity onPress={() => setSelectChart(ChartType.MakeMoney)}>
                            <Text style={styles.tabTextAction}>Báo cáo doanh thu</Text>
                        </TouchableOpacity>
                    </LinearGradient>
                ) : (
                    <TouchableOpacity style={styles.tab} onPress={() => setSelectChart(ChartType.MakeMoney)}>
                        <Text style={styles.tabText}>Báo cáo doanh thu</Text>
                    </TouchableOpacity>
                )}

                {SelectChart == ChartType.ProductData ? (
                    <LinearGradient
                        colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
                        style={styles.tabAction}
                    >
                        <TouchableOpacity onPress={() => setSelectChart(ChartType.ProductData)}>
                            <Text style={styles.tabTextAction}>Báo cáo sản phẩm</Text>
                        </TouchableOpacity>
                    </LinearGradient>
                ) : (
                    <TouchableOpacity style={styles.tab} onPress={() => setSelectChart(ChartType.ProductData)}>
                        <Text style={styles.tabText}>Báo cáo sản phẩm</Text>
                    </TouchableOpacity>
                )}

                {SelectChart == ChartType.OrderData ? (
                    <LinearGradient
                        colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
                        style={styles.tabAction}
                    >
                        <TouchableOpacity onPress={() => setSelectChart(ChartType.OrderData)}>
                            <Text style={styles.tabTextAction}>Báo cáo đơn hàng</Text>
                        </TouchableOpacity>
                    </LinearGradient>
                ) : (
                    <TouchableOpacity style={styles.tab} onPress={() => setSelectChart(ChartType.OrderData)}>
                        <Text style={styles.tabText}>Báo cáo đơn hàng</Text>
                    </TouchableOpacity>
                )}
            </ScrollView>
            {SelectChart === ChartType.MakeMoney && <ChartMoney />}
            {SelectChart === ChartType.ProductData && <ChartProduct />}
            {SelectChart === ChartType.OrderData && <ChartOrder />}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        padding: 10,
        backgroundColor: '#fff',
        borderRadius: 8,
        margin: 10,
    },
    tabContainer: {
        flexDirection: 'row',
        marginBottom: 10,
    },
    tab: {
        padding: 4,
        borderBottomWidth: 2,
        borderBottomColor: 'transparent',
        marginLeft: 5,
        marginRight: 5,
        fontFamily: 'Roboto'
    },
    tabAction: {
        padding: 4,
        borderBottomWidth: 2,
        borderBottomColor: 'transparent',
        marginLeft: 5,
        marginRight: 5,
        borderRadius: 10,
        fontFamily: 'Roboto',
        color: ColorThemes.light.primary_main_color,

    },
    tabTextAction: {
        fontSize: 16,
        color: 'blue',
        padding: 3
    },
    tabText: {
        fontSize: 16,
        color: '#696969',
        padding: 3
    },
});

export default Chart;