import io, { Socket } from 'socket.io-client';
import { ChatMessage, ChatRoom } from '../types/ChatTypes';
import ConfigAPI from '../../../Config/ConfigAPI';

class SocketService {
  private socket: Socket | null = null;
  private isConnected: boolean = false;

  connect(CustomerId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // Sử dụng ConfigAPI.Socketurl thay vì ConfigAPI.url
        console.log('Connecting to socket:', ConfigAPI.Socketurl);
        this.socket = io(ConfigAPI.Socketurl, {
          auth: {
            CustomerId,
          },
          transports: ['websocket'],
          timeout: 20000,
          forceNew: true,
        });
        this.socket.on('connection', () => {
          console.log('Socket connected');
          this.isConnected = true;
          resolve();
        });

        this.socket.on('disconnect', () => {
          console.log('Socket disconnected');
          this.isConnected = false;
        });

        this.socket.on('connect_error', (error) => {
          console.error('Socket connection error:', error);
          this.isConnected = false;
          reject(error);
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  joinRoom(roomId: string) {
    if (this.socket && this.isConnected) {
      console.log('Joining room:', roomId);
      this.socket.emit('join-rooms', { roomId });
    }
  }

  leaveRoom(roomId: string) {
    if (this.socket && this.isConnected) {
      console.log('Leaving room:', roomId);
      this.socket.emit('leave-rooms', { roomId });
    }
  }

  sendMessage(roomId: string, message: any) {
    if (this.socket && this.isConnected) {
      console.log('Sending message to room:', roomId, message);
      this.socket.emit('send-message', {
        roomId,
        message,
      });
    }
  }

  onReceiveMessage(callback: (data: { roomId: string; fromUserId: string; message: any }) => void) {
    if (this.socket) {
      this.socket.on('receive-message', callback);
    }
  }

  onUserTyping(callback: (data: { roomId: string; fromUserId: string }) => void) {
    if (this.socket) {
      this.socket.on('typing', callback);
    }
  }

  sendTyping(roomId: string) {
    if (this.socket && this.isConnected) {
      console.log('Sending typing to room:', roomId);
      this.socket.emit('typing', { roomId });
    }
  }

  onOnlineUsers(callback: (users: any[]) => void) {
    if (this.socket) {
      this.socket.on('onlineUsers', callback);
    }
  }

  onUserOnline(callback: (customerId: string) => void) {
    if (this.socket) {
      this.socket.on('user-online', callback);
    }
  }

  onUserOffline(callback: (customerId: string) => void) {
    if (this.socket) {
      this.socket.on('user-offline', callback);
    }
  }

  removeAllListeners() {
    if (this.socket) {
      this.socket.removeAllListeners();
    }
  }

  getConnectionStatus(): boolean {
    return this.isConnected;
  }
}

export default new SocketService();
