import React, {useState} from 'react';
import {Text, FlatList, TouchableOpacity, StyleSheet, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {ColorThemes} from '../../../assets/skin/colors';

interface Props {
  data: any[];
  onChangeTab: (tabId: string) => void;
}

const ScrollableTabs = ({data, onChangeTab}: Props) => {
  const [activeTabId, setActiveTabId] = useState(
    Array.isArray(data) && data.length > 0 ? data[0].id : '',
  );

  const handleTabPress = (tabId: string) => {
    try {
      if (!tabId || tabId === activeTabId) return;
      setActiveTabId(tabId);
      if (onChangeTab && typeof onChangeTab === 'function') {
        onChangeTab(tabId);
      }
    } catch (error) {
      console.error('Error in handleTabPress:', error);
    }
  };

  const renderTabItem = ({item}: {item: any}) => {
    const isActive = item.id === activeTabId;
    const TabContent = () => (
      <View style={styles.tabItem}>
        <FontAwesomeIcon
          icon={item.icon}
          size={16}
          color={isActive ? ColorThemes.light.primary_main_color : '#333'}
          style={styles.tabIcon}
        />
        <Text
          style={[
            styles.tabLabel,
            isActive && {color: ColorThemes.light.primary_main_color},
          ]}>
          {item.label}
        </Text>
      </View>
    );

    return (
      <TouchableOpacity
        style={{
          borderRadius: 10,
          paddingHorizontal: 10,
        }}
        onPress={() => handleTabPress(item.id)}>
        {isActive ? (
          <LinearGradient
            colors={['#90C8FB', '#8DC4F7E5', '#B6F5FE']}
            start={{x: 0, y: 0.5}}
            end={{x: 1, y: 0.5}}
            style={{borderRadius: 10}}>
            <TabContent />
          </LinearGradient>
        ) : (
          <TabContent />
        )}
      </TouchableOpacity>
    );
  };

  // Don't render if data is invalid
  if (!Array.isArray(data) || data.length === 0) {
    return <View />;
  }

  return (
    <View>
      <FlatList
        data={data}
        renderItem={renderTabItem}
        keyExtractor={(item: any, index: number) =>
          item?.id ? `${item.id}_${index}` : `tab_${index}`
        }
        horizontal={true} // Quan trọng: để list cuộn ngang
        showsHorizontalScrollIndicator={false} // Ẩn thanh cuộn ngang cho giao diện sạch hơn
        style={styles.listContentContainer}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  listContentContainer: {
    paddingHorizontal: 10,
    paddingVertical: 10,
  },
  tabItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 10,
    borderRadius: 10,
  },
  tabIcon: {
    marginRight: 8,
  },
  tabLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333', // Màu chữ mặc định
  },
  activeTabLabel: {
    color: '#007bff', // Màu chữ khi được chọn
  },
});

export default ScrollableTabs;
