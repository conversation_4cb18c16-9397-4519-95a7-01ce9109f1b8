import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {
  Image,
  Pressable,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {Text, TextInput} from 'react-native-paper';
import {
  AppSvg,
  ComponentStatus,
  FLoading,
  Rating,
  showSnackbar,
  Winicon,
} from 'wini-mobile-components';
import {TextFieldForm} from '../../modules/news/form/component-form';
import {useForm} from 'react-hook-form';
import {ColorThemes} from '../../assets/skin/colors';
import {FlatList, ScrollView} from 'react-native-gesture-handler';
import Footer from '../../Screen/Layout/footer';
import WScreenFooter from '../../Screen/Layout/footer';
import ImageCropPicker from 'react-native-image-crop-picker';
import {BaseDA} from '../../base/BaseDA';
import iconSvg from '../../svg/icon';
import {DataController} from '../../base/baseController';
import {randomGID} from '../../utils/Utils';
import {useNavigation, useRoute} from '@react-navigation/native';
import {navigate, RootScreen} from '../../router/router';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {TypoSkin} from '../../assets/skin/typography';
import ConfigAPI from '../../Config/ConfigAPI';
import {sumArray} from '../../utils/arrayUtils';
const CreateReviewOrderDetail = () => {
  const route = useRoute<any>();
  const navigation = useNavigation<any>();
  const methods = useForm({shouldFocusError: false});
  const [productImages, setProductImages] = useState<{[key: string]: string[]}>(
    {},
  );
  const [productImageViews, setProductImageViews] = useState<{
    [key: string]: string[];
  }>({});
  const [productRatings, setProductRatings] = useState<{[key: string]: number}>(
    {},
  );
  const CustomerInfo = useSelectorCustomerState().data;
  const [errorStar, setErrorStar] = useState<boolean>(false);
  const [errorImage, setErrorImage] = useState<boolean>(false);
  const [listProduct, setListProduct] = useState<any[]>([]);

  const RatingController = useMemo(() => new DataController('Rating'), []);
  const ProductController = useMemo(() => new DataController('Product'), []);

  let textFieldStyle = {
    height: 70,
    paddingLeft: 8,
    paddingRight: 8,
    borderWidth: 0,
  };

  const getProductInfoINOrder = async (productId: string[]) => {
    let res = await ProductController.getByListId(productId);
    if (res.code == 200) {
      console.log('check-res', res.data);

      setListProduct(res.data);
    }
  };

  useEffect(() => {
    console.log('prodcutId', route?.params?.Data);
    getProductInfoINOrder([route?.params?.Data?.ProductId]);
  }, [route?.params?.Data]);
  const onSubmit = async (data: any) => {
    let RatingOrderValue: number[] = [];
    if (Object.keys(productRatings).length === 0) {
      setErrorStar(true);
      return;
    } else {
      setErrorStar(false);
    }

    let hasImages = false;
    for (const product of listProduct) {
      if (productImages[product.Id]?.length > 0) {
        hasImages = true;
        break;
      }
    }
    if (!hasImages) {
      setErrorImage(true);
      return;
    } else {
      setErrorImage(false);
    }

    // Create reviews for each product
    for (const product of listProduct) {
      if (data[`Content_${product.Id}`]) {
        let res = await RatingController.add([
          {
            Id: randomGID(),
            DateCreated: Date.now(),
            Description: data[`Content_${product.Id}`],
            Value: productRatings[product.Id],
            Img: productImages[product.Id]?.[0]?.toString(),
            ListImg: productImages[product.Id]?.toString(),
            ProductId: product.Id,
            OrderId: route?.params?.Data?.Id,
            ShopId: route?.params?.Data.ShopId,
            Name: route?.params?.Data.Name,
            CustomerId: CustomerInfo?.Id,
            Type: 2,
          },
        ]);
        if (res.code === 200) {
          RatingOrderValue.push(productRatings[product.Id]);
        }
      }

      // Create order rating
      if (RatingOrderValue.length > 0) {
        let createOrderRating = await RatingController.add([
          {
            Id: randomGID(),
            DateCreated: Date.now(),
            Value: sumArray(RatingOrderValue) / RatingOrderValue.length,
            ShopId: route?.params?.Data.ShopId,
            OrderId: route?.params?.Data?.Id,
            Name: route?.params?.Data.Name,
            CustomerId: CustomerInfo?.Id,
            Type: 1,
          },
        ]);
        if (createOrderRating.code === 200) {
          showSnackbar({
            message: 'Đánh giá thành công',
            status: ComponentStatus.SUCCSESS,
          });
          navigation.goBack();
        }
      }
    }
  };
  const pickerImg = async (productId: string) => {
    try {
      const img = await ImageCropPicker.openPicker({
        multiple: true,
        cropping: false,
        includeBase64: true,
      });

      if (img) {
        let arrayImage: any[] = [];
        let newImageViews: string[] = [];

        img.forEach((item: any) => {
          newImageViews.push(item?.path);
          arrayImage.push({
            uri: item?.path,
            type: item?.mime,
            name: item?.filename ?? 'file',
          });
        });

        const resImgs = await BaseDA.uploadFiles(arrayImage);
        if (resImgs.length > 0) {
          setProductImages(prev => ({
            ...prev,
            [productId]: [
              ...(prev[productId] || []),
              ...resImgs.map((img: any) => img.Id),
            ],
          }));
          setProductImageViews(prev => ({
            ...prev,
            [productId]: [...(prev[productId] || []), ...newImageViews],
          }));
        }
      }
    } catch (error) {
      console.error('Error picking image:', error);
    }
  };

  const handleDeleteImage = (productId: string, imagePath: string) => {
    setProductImageViews(prev => ({
      ...prev,
      [productId]: prev[productId].filter(img => img !== imagePath),
    }));
    // You might want to also remove the corresponding image ID from productImages
  };
  return (
    <View style={{flex: 1}}>
      <ScrollView>
        <FlatList
          data={listProduct}
          style={{flex: 1, paddingBottom: 100}}
          keyExtractor={(item, i) => `${i} ${item.Id}`}
          renderItem={({item, index}) => (
            <Pressable key={`title-${index}`}>
              <View style={styles.container}>
                <View style={styles.Reaction}>
                  <View style={styles.header}>
                    <Text
                      style={{
                        fontSize: 20,
                        fontWeight: '500',
                        fontFamily: 'roboto',
                        color: ColorThemes.light.text_primary_color,
                        flex: 0.7,
                      }}>
                      Đơn hàng #{route?.params?.Data?.Id}
                    </Text>
                    <Text
                      style={{
                        fontSize: 16,
                        fontWeight: 'bold',
                        color: ColorThemes.light.success_main_color,
                        flex: 0.3,
                        textAlign: 'right',
                        alignItems: 'center',
                      }}>
                      Hoàn thành
                    </Text>
                  </View>
                  <View>
                    <View style={{flexDirection: 'row', alignItems: 'center'}}>
                      <Image
                        source={{
                          uri: ConfigAPI.urlImg + item?.Img,
                        }}
                        style={styles.avatar}
                      />
                      <View>
                        <Text style={styles.name}>{item?.Name}</Text>
                        <View>
                          <Rating
                            value={productRatings[item.Id] || 0}
                            size={20}
                            onChange={value =>
                              setProductRatings(prev => ({
                                ...prev,
                                [item.Id]: value,
                              }))
                            }
                          />
                        </View>
                        {errorStar && (
                          <Text style={styles.errorText}>
                            Vui lòng nhập đánh giá
                          </Text>
                        )}
                      </View>
                    </View>
                  </View>
                </View>
                <View style={{position: 'relative'}}>
                  <View style={styles.WrapperInput}>
                    <TextFieldForm
                      control={methods.control}
                      name={`Content_${item.Id}`}
                      placeholder="Nhập nội dung đánh giá"
                      returnKeyType="done"
                      textFieldStyle={textFieldStyle}
                      errors={methods.formState.errors}
                      register={methods.register}
                      required
                      style={styles.inputAction}
                      suffix={
                        <TouchableOpacity
                          style={{padding: 12}}
                          onPress={() => pickerImg(item.Id)}>
                          <Winicon src="outline/files/file-image" size={20} />
                        </TouchableOpacity>
                      }
                    />
                  </View>
                </View>
                {errorImage && !productImages[item.Id]?.length && (
                  <Text style={styles.errorText}>Vui lòng chọn ảnh</Text>
                )}
                {productImageViews[item.Id]?.length > 0 && (
                  <View
                    style={{
                      flexDirection: 'row',
                      gap: 10,
                      display: 'flex',
                      flexWrap: 'wrap',
                    }}>
                    {productImageViews[item.Id]?.map(
                      (imagePath: string, imgIndex: number) => (
                        <View key={imgIndex} style={{marginTop: 10}}>
                          <Image
                            source={{uri: imagePath}}
                            style={{width: 121, height: 101, borderRadius: 5}}
                          />
                          <TouchableOpacity
                            style={{
                              position: 'absolute',
                              bottom: 0,
                              left: '50%',
                              transform: [{translateX: -10}, {translateY: 11}],
                            }}
                            onPress={() =>
                              handleDeleteImage(item.Id, imagePath)
                            }>
                            <AppSvg SvgSrc={iconSvg.exit} size={24} />
                          </TouchableOpacity>
                        </View>
                      ),
                    )}
                  </View>
                )}
              </View>
            </Pressable>
          )}
        />
      </ScrollView>
      <WScreenFooter style={{width: '100%', paddingHorizontal: 20}}>
        <TouchableOpacity
          style={styles.buyButton}
          onPress={methods.handleSubmit(onSubmit)}>
          <Text style={styles.actionButtonText}>Gửi đánh giá</Text>
        </TouchableOpacity>
      </WScreenFooter>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {paddingTop: 10, paddingLeft: 10, paddingRight: 10, flex: 1},
  Reaction: {
    flexDirection: 'column',
    gap: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
    marginTop: 10,
  },
  avatar: {
    borderWidth: 5,
    borderRadius: 50,
    width: 50,
    height: 50,
    marginRight: 10,
    borderColor: 'white',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  name: {
    fontWeight: '700',
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_title_color,
  },
  WrapperInput: {
    marginVertical: 10,
    width: '100%',
    paddingTop: 10,
    paddingBottom: 10,
  },
  inputAction: {
    borderRadius: 10,
    fontSize: 16,
    color: '#000',
    backgroundColor: 'white',
    borderColor: ColorThemes.light.neutral_main_background_color,
    borderWidth: 1,
  },
  input: {
    borderRadius: 10,
    fontSize: 16,
    color: '#000',
    backgroundColor: 'white',
    borderWidth: 2,
    borderColor: ColorThemes.light.error_main_color,
  },
  errorText: {
    color: ColorThemes.light.error_main_color,
    marginTop: 5,
    fontSize: 12,
  },
  buyButton: {
    backgroundColor: 'blue',
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 50,
    borderRadius: 30,
  },

  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default CreateReviewOrderDetail;
