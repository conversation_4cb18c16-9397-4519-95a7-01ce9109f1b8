import {
  ActivityIndicator,
  Dimensions,
  FlatList,
  RefreshControl,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import {RootState} from '../../../../redux/store/store';
import {useDispatch, useSelector} from 'react-redux';
import {useCallback, useEffect} from 'react';
import {Product} from '../../../../redux/models/product';
import ProductCard from '../../card/ProductCard';
import React from 'react';
import {ColorThemes} from '../../../../assets/skin/colors';
import EmptyPage from '../../../../Screen/emptyPage';
import {fetchProducts} from '../../../../redux/actions/productAction';
import {RootScreen} from '../../../../router/router';
import {useNavigation} from '@react-navigation/native';

const {width} = Dimensions.get('window');

const ListProduct = () => {
  const navigation = useNavigation<any>();
  const dispatch = useDispatch();
  const {data, onLoading} = useSelector((state: RootState) => state.product);
  const {filter} = useSelector((state: RootState) => state.productByCategory);

  // effect get sản phẩm
  useEffect(() => {
    const config: any = {
      page: 1,
      size: 10,
      searchRaw: '',
      sortby: [],
      query: '',
    };
    if (filter.categoryId) config.query = `@CategoryId:{${filter.categoryId}}`;

    if (filter.activeFilters.IsHot) config.query += ` @IsHot:{true}`;
    if (filter.activeFilters.IsFreeShip) config.query += ` @IsFreeShip:{true}`;

    if (filter.maxPrice) {
      config.searchRaw += `@Price:[0 ${filter.maxPrice}] `;
    }

    if (filter.textSearch && filter.textSearch.length)
      config.searchRaw += `(@Name:*${filter.textSearch}*) `;

    if (filter.brandId) config.query += ` @BrandId:{${filter.brandId}}`;

    if (filter.sortOption) {
      switch (filter.sortOption) {
        case 'newest':
          config.sortby = [{prop: 'DateCreated', direction: 'DESC'}];
          break;
        case 'price_asc':
          config.sortby = [{prop: 'Price', direction: 'ASC'}];
          break;
        case 'price_desc':
          config.sortby = [{prop: 'Price', direction: 'DESC'}];
          break;
        default:
          break;
      }
    }

    if (!config.searchRaw?.length) delete config.searchRaw;
    if (!config.sortby?.length) delete config.sortby;
    if (!config.query?.length) delete config.query;

    dispatch(fetchProducts(config) as any);
  }, [filter, dispatch]);

  // xem chi tiết sản phẩm
  const handleProductPress = useCallback((product: Product) => {
    navigation.navigate(RootScreen.ProductDetail, {id: product.Id});
  }, []);

  // render item sản phẩm
  const renderProductItem = useCallback(
    ({item}: {item: Product}) => (
      <View style={{marginHorizontal: 8}}>
        <ProductCard
          item={item}
          onPress={handleProductPress}
          width={(width - 48) / 2}
          height={((width - 48) / 2) * 1.8}
        />
      </View>
    ),
    [],
  );

  // hiển thị loading
  if (onLoading) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <ActivityIndicator
          size="large"
          color={ColorThemes.light.primary_main_color}
        />
      </View>
    );
  }

  // hiển thị trang trống
  if (data.length === 0) {
    return <EmptyPage />;
  }

  return (
    <FlatList
      data={data}
      renderItem={renderProductItem}
      keyExtractor={(item, index) => item.Id || index.toString()}
      numColumns={2}
      contentContainerStyle={{paddingHorizontal: 8, paddingBottom: 16}}
      ListHeaderComponent={() => (
        <Text
          style={{
            fontSize: 16,
            fontWeight: 'bold',
            marginLeft: 8,
            marginTop: 16,
            marginBottom: 12,
          }}>
          Cửa hàng gợi ý
        </Text>
      )}
      refreshControl={
        <RefreshControl
          refreshing={false}
          onRefresh={() => {}}
          colors={[ColorThemes.light.primary_main_color]}
          tintColor={ColorThemes.light.primary_main_color}
        />
      }
      onEndReached={() => {}}
      onEndReachedThreshold={0.1}
      showsVerticalScrollIndicator={false}
      removeClippedSubviews={true} // Tối ưu performance
      maxToRenderPerBatch={10} // Giới hạn số item render mỗi batch
      windowSize={10} // Tối ưu memory
    />
  );
};

export default ListProduct;
