import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { <PERSON><PERSON><PERSON>, LineChart } from 'react-native-chart-kit';
import { <PERSON><PERSON>View } from 'react-native-gesture-handler';
import { FLoading } from 'wini-mobile-components';
import ProductDA from '../../../modules/Product/da';
import { useSelectorShopState } from '../../../redux/hook/shopHook ';
import { StatusOrder } from '../../../Config/Contanst';
import { OrderDA } from '../../../modules/order/orderDA';

const { width } = Dimensions.get('window');
const ChartProduct = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [weeklyData, setWeeklyData] = useState<number[]>([0, 0, 0, 0, 0, 0, 0]);
  const [weeklyData2, setWeeklyData2] = useState<number[]>([
    0, 0, 0, 0, 0, 0, 0,
  ]);
  const [monthlyData, setMonthlyData] = useState<number[]>([
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  ]);
  const [monthlyData2, setMonthlyData2] = useState<number[]>([
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  ]);
  const [yearData, setYearData] = useState<number[]>([
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  ]);
  const [yearData2, setYearData2] = useState<number[]>([
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  ]);
  const shopInfo = useSelectorShopState().data;
  const orderDA = new OrderDA();
  const [selectChart, setSelectChart] = useState<string>('');

  const handleSelectChart = (type: string) => {
    setSelectChart(type);
  };

  // Function to format Y-axis labels with shortened values



  // Data for BarChart (Income vs Spend)
  const lineDataOne = {
    labels: [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday',
    ],
    datasets: [
      {
        data: weeklyData,
        color: (opacity = 1) => `rgba(0, 123, 255, ${opacity})`,
        strokeWidth: 2,
      },
      {
        data: weeklyData2,
        color: (opacity = 1) => `rgba(255, 99, 132, ${opacity})`,
        strokeWidth: 2,
      },
    ],
  };

  // Data for LineChart (Month vs Year)
  const lineDataThree = {
    labels: (() => {
      const currentDate = dayjs();
      const daysInMonth = currentDate.daysInMonth();
      return Array.from({ length: daysInMonth }, (_, i) => (i + 1).toString());
    })(),
    datasets: [
      {
        data: monthlyData, // Monthly data from API
        color: (opacity = 1) => `rgba(0, 123, 255, ${opacity})`,
        strokeWidth: 2,
      },
      {
        data: monthlyData2, // Yearly data from API
        color: (opacity = 1) => `rgba(255, 99, 132, ${opacity})`,
        strokeWidth: 2,
      },
    ],
  };

  const lineDataTwo = {
    labels: [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ],
    datasets: [
      {
        data: yearData, // Monthly data from API for the year
        color: (opacity = 1) => `rgba(0, 123, 255, ${opacity})`,
        strokeWidth: 2,
      },
      {
        data: yearData2, // Monthly commission data from API for the year
        color: (opacity = 1) => `rgba(255, 99, 132, ${opacity})`,
        strokeWidth: 2,
      },
    ],
  };

  let getanalysisDateWeek = async () => {
    try {
      const currentDate = dayjs();
      const startOfWeek = currentDate.startOf('week');
      const endOfWeek = currentDate.endOf('week');

      if (!shopInfo || !shopInfo[0]?.Id) {
        console.log('No shop info available');
        setIsLoading(false);
        return;
      }

      let res = await orderDA.getOrderByShopId(
        shopInfo[0]?.Id,
        StatusOrder.success,
      );

      if (res?.code === 200) {
        // lấy số tiền của đơn hàng
        let listOrderDetailId: string[] = [];
        let dataDay = res?.data.filter(
          (item: any) => {
            const itemDate = dayjs(item?.DateCreated);
            return (itemDate.isAfter(startOfWeek.subtract(1, 'day')) ||
              itemDate.isSame(startOfWeek, 'day')) &&
              (itemDate.isBefore(endOfWeek.add(1, 'day')) ||
                itemDate.isSame(endOfWeek, 'day'));
          }
        );
        for (let item of dataDay) {
          listOrderDetailId.push(item?.Id);
        }

        // Initialize arrays for weekly data
        let newWeeklyData = [0, 0, 0, 0, 0, 0, 0];
        let newWeeklyData2 = [0, 0, 0, 0, 0, 0, 0];
        let dataMoney: any[] = [];

        if (listOrderDetailId.length > 0) {
          let res = await orderDA.getMoneyDetailsByOrderDetailId(
            listOrderDetailId.toString(),
          );
          if (res?.code === 200) {
            // lấy số tiền hoa hồng của đơn hàng
            dataMoney = res?.data.filter(
              (item: any) => {
                const itemDate = dayjs(item?.DateCreated);
                return (itemDate.isAfter(startOfWeek.subtract(1, 'day')) ||
                  itemDate.isSame(startOfWeek, 'day')) &&
                  (itemDate.isBefore(endOfWeek.add(1, 'day')) ||
                    itemDate.isSame(endOfWeek, 'day'));
              }
            );
          }
        }

        for (let item of dataDay) {
          // Get the day of week (0-6, where 0 is Sunday)
          const dayOfWeek = dayjs(item?.DateCreated).day();

          // Convert Sunday (0) to index 6, Monday (1) to index 0, etc.
          // This maps: Sunday=6, Monday=0, Tuesday=1, ..., Saturday=5
          const dayIndex = dayOfWeek === 0 ? 6 : dayOfWeek - 1;

          const orderAmount =
            Number(item?.Total || 0);

          // Add the order amount to the corresponding day
          newWeeklyData[dayIndex] += orderAmount;
          //   newWeeklyData2[dayIndex] += orderAmount * 0.8; // Different calculation for second dataset
        }

        for (let item of dataMoney) {
          const dayOfWeek = dayjs(item?.DateCreated).day();
          const dayIndex = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
          const orderCommission = Number(item?.Value || 0);
          newWeeklyData2[dayIndex] += orderCommission;
        }

        setWeeklyData(newWeeklyData);
        setWeeklyData2(newWeeklyData2);
      } else {
        console.log('Failed to fetch order data:', res);
      }
    } catch (error) {
      console.error('Error in getanalysisDateWeek:', error);
    } finally {
      setIsLoading(false);
    }
  };

  let getanalysisDateCurrentMonth = async () => {
    try {
      const currentDate = dayjs();
      const startOfMonth = currentDate.startOf('month');
      const endOfMonth = currentDate.endOf('month');

      if (!shopInfo || !shopInfo[0]?.Id) {
        console.log('No shop info available');
        return;
      }

      let res = await orderDA.getOrderByShopId(
        shopInfo[0]?.Id,
        StatusOrder.success,
      );

      if (res?.code === 200) {
        // lấy số tiền của đơn hàng
        let listOrderDetailId: string[] = [];
        let dataCurrentMonth = res?.data.filter(
          (item: any) => {
            const itemDate = dayjs(item?.DateCreated);
            return (itemDate.isAfter(startOfMonth.subtract(1, 'day')) ||
              itemDate.isSame(startOfMonth, 'day')) &&
              (itemDate.isBefore(endOfMonth.add(1, 'day')) ||
                itemDate.isSame(endOfMonth, 'day'));
          }
        );
        for (let item of dataCurrentMonth) {
          listOrderDetailId.push(item?.Id);
        }

        // Initialize arrays for current month data (days in current month)
        const daysInMonth = currentDate.daysInMonth();
        let newCurrentMonthData = new Array(daysInMonth).fill(0);
        let newCurrentMonthData2 = new Array(daysInMonth).fill(0);
        let dataMoney: any[] = [];

        if (listOrderDetailId.length > 0) {
          let res = await orderDA.getMoneyDetailsByOrderDetailId(
            listOrderDetailId.toString(),
          );
          if (res?.code === 200) {
            // lấy số tiền hoa hồng của đơn hàng
            dataMoney = res?.data.filter(
              (item: any) => {
                const itemDate = dayjs(item?.DateCreated);
                return (itemDate.isAfter(startOfMonth.subtract(1, 'day')) ||
                  itemDate.isSame(startOfMonth, 'day')) &&
                  (itemDate.isBefore(endOfMonth.add(1, 'day')) ||
                    itemDate.isSame(endOfMonth, 'day'));
              }
            );
          }
        }

        for (let item of dataCurrentMonth) {
          // Get the day of month (1-31)
          const dayOfMonth = dayjs(item?.DateCreated).date();
          const dayIndex = dayOfMonth - 1; // Convert to 0-based index

          const orderAmount = Number(item?.Total || 0);

          // Add the order amount to the corresponding day
          newCurrentMonthData[dayIndex] += orderAmount;
        }

        for (let item of dataMoney) {
          const dayOfMonth = dayjs(item?.DateCreated).date();
          const dayIndex = dayOfMonth - 1; // Convert to 0-based index
          newCurrentMonthData2[dayIndex] += Number(item?.Total || 0);
        }

        setMonthlyData(newCurrentMonthData);
        setMonthlyData2(newCurrentMonthData2);
      } else {
        console.log('Failed to fetch order data:', res);
      }
    } catch (error) {
      console.error('Error in getanalysisDateCurrentMonth:', error);
    }
  };

  let getanalysisDateYear = async () => {
    try {
      const currentDate = dayjs();
      const startOfYear = currentDate.startOf('year');
      const endOfYear = currentDate.endOf('year');

      if (!shopInfo || !shopInfo[0]?.Id) {
        console.log('No shop info available');
        return;
      }

      let res = await orderDA.getOrderByShopId(
        shopInfo[0]?.Id,
        StatusOrder.success,
      );

      if (res?.code === 200) {
        // lấy số tiền của đơn hàng
        let listOrderDetailId: string[] = [];
        let dataMonth = res?.data.filter(
          (item: any) => {
            const itemDate = dayjs(item?.DateCreated);
            return (itemDate.isAfter(startOfYear.subtract(1, 'day')) ||
              itemDate.isSame(startOfYear, 'day')) &&
              (itemDate.isBefore(endOfYear.add(1, 'day')) ||
                itemDate.isSame(endOfYear, 'day'));
          }
        );
        for (let item of dataMonth) {
          listOrderDetailId.push(item?.Id);
        }

        // Initialize arrays for monthly data
        let newMonthlyData = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
        let newMonthlyData2 = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
        let dataMoney: any[] = [];

        if (listOrderDetailId.length > 0) {
          let res = await orderDA.getMoneyDetailsByOrderDetailId(
            listOrderDetailId.toString(),
          );
          if (res?.code === 200) {
            // lấy số tiền hoa hồng của đơn hàng
            dataMoney = res?.data.filter(
              (item: any) => {
                const itemDate = dayjs(item?.DateCreated);
                return (itemDate.isAfter(startOfYear.subtract(1, 'day')) ||
                  itemDate.isSame(startOfYear, 'day')) &&
                  (itemDate.isBefore(endOfYear.add(1, 'day')) ||
                    itemDate.isSame(endOfYear, 'day'));
              }
            );
          }
        }

        for (let item of dataMonth) {
          // Get the month (0-11, where 0 is January)
          const yearIndex = dayjs(item?.DateCreated).month();

          const orderAmount =
            Number(item?.Quantity || 0) * Number(item?.Price || 0);

          // Add the order amount to the corresponding month
          newMonthlyData[yearIndex] += Number(item?.Total || 0);
        }

        for (let item of dataMoney) {
          const yearIndex = dayjs(item?.DateCreated).month();
          const orderCommission = Number(item?.Value || 0);
          newMonthlyData2[yearIndex] += Number(item?.Total || 0);
        }

        setYearData(newMonthlyData);
        setYearData2(newMonthlyData2);
      } else {
        console.log('Failed to fetch order data:', res);
      }
    } catch (error) {
      console.error('Error in getanalysisDateMonth:', error);
    }
  };

  useEffect(() => {
    setIsLoading(true);
    getanalysisDateWeek();
    getanalysisDateCurrentMonth();
    getanalysisDateYear();
  }, []);

  useEffect(() => {
    if (selectChart === 'month') {
      getanalysisDateCurrentMonth();
    } else if (selectChart === 'year') {
      getanalysisDateYear();
    }
  }, [selectChart]);

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <FLoading visible={isLoading} />
      <View style={styles.header}>
        <TouchableOpacity>
          <Text style={styles.title}>Weekly</Text>
        </TouchableOpacity>
        <View style={{ flexDirection: 'column', marginTop: 10 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <View
              style={{
                width: 20,
                height: 20,
                backgroundColor: '#007bff',
                marginRight: 10,
              }}></View>
            <Text>Số tiền thu được từ đơn hàng</Text>
          </View>
          <View
            style={{ flexDirection: 'row', alignItems: 'center', marginTop: 10 }}>
            <View
              style={{
                width: 20,
                height: 20,
                backgroundColor: '#ff6384',
                marginRight: 10,
              }}></View>
            <Text>Số tiền hoa hồng trả cho khách hàng</Text>
          </View>
        </View>
      </View>
      <ScrollView style={styles.chartContainer} horizontal={true} showsHorizontalScrollIndicator={false}>
        <LineChart
          key={`weekly-chart-${weeklyData.join('-')}-${weeklyData2.join('-')}`}
          data={lineDataOne}
          width={700}
          height={300}
          chartConfig={{
            backgroundColor: '#fff',
            backgroundGradientFrom: '#fff',
            backgroundGradientTo: '#fff',
            decimalPlaces: 0,
            color: (opacity = 1) => `rgba(0, 123, 255, ${opacity})`,
            labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
            style: {
              borderRadius: 8,
            },
            propsForDots: {
              r: '3',
              strokeWidth: '3',
              stroke: '#007bff',
            },
            fillShadowGradient: '#e6f0fa',
            fillShadowGradientOpacity: 0.7,
          }}
          style={{
            marginVertical: 8,
            borderRadius: 8,
          }}
          withInnerLines={true}
          yAxisLabel=""
          yAxisSuffix=""
          bezier
          withVerticalLabels={true}
          withHorizontalLabels={true}
          withDots={true}
          withShadow={false}
          segments={5}
          formatYLabel={(value) => {
            const numValue = parseFloat(value);
            if (numValue >= 1000000) {
              return `${(numValue / 1000000).toFixed(2)} M`;
            } else if (numValue >= 1000) {
              return `${(numValue / 1000).toFixed(2)} K`;
            }
            return numValue.toString();
          }}
        />
      </ScrollView>
      <View style={styles.headerTwo}>
        <TouchableOpacity onPress={() => handleSelectChart('month')}>
          <Text style={styles.titleOne}>Month</Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={() => handleSelectChart('year')}>
          <Text style={styles.titleTwo}>Year</Text>
        </TouchableOpacity>
      </View>
      <ScrollView style={styles.chartContainer} horizontal={true} showsHorizontalScrollIndicator={false}>
        <LineChart
          key={`monthly-chart-${selectChart}-${monthlyData.join('-')}-${monthlyData2.join('-')}`}
          data={selectChart === 'year' ? lineDataTwo : lineDataThree}
          width={1000}
          height={300}
          chartConfig={{
            backgroundColor: '#fff',
            backgroundGradientFrom: '#fff',
            backgroundGradientTo: '#fff',
            decimalPlaces: 0,
            color: (opacity = 1) => `rgba(0, 123, 255, ${opacity})`,
            labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
            style: {
              borderRadius: 8,
            },
            propsForDots: {
              r: '3',
              strokeWidth: '3',
              stroke: '#007bff',
            },
            fillShadowGradient: '#e6f0fa',
            fillShadowGradientOpacity: 0.7,
          }}
          style={{
            marginVertical: 8,
            borderRadius: 8,
          }}
          withInnerLines={true}
          yAxisLabel=""
          yAxisSuffix=""
          bezier
          withVerticalLabels={true}
          withHorizontalLabels={true}
          withDots={true}
          withShadow={false}
          segments={5}
          formatYLabel={(value) => {
            const numValue = parseFloat(value);
            if (numValue >= 1000000) {
              return `${(numValue / 1000000).toFixed(2)} M`;
            } else if (numValue >= 1000) {
              return `${(numValue / 1000).toFixed(2)} K`;
            }
            return numValue.toString();
          }}
        />
      </ScrollView>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 8,
    height: 700,
  },
  header: {
    flexDirection: 'column',
    alignContent: 'center',
    justifyContent: 'space-around',
    margin: 'auto',
    marginTop: 2,
  },
  headerTwo: {
    flexDirection: 'row',
    alignContent: 'center',
    justifyContent: 'space-around',
    margin: 'auto',
    marginTop: 2,
    paddingBottom: 30
  },
  title: {
    fontSize: 14,
    fontWeight: 'bold',
    borderRightWidth: 1,
    padding: 7,
    margin: 'auto',
    width: 119.3,
    height: 29.82,
    backgroundColor: 'blue',
    textAlign: 'center',
    color: 'white',
    borderRadius: 50,
  },
  titleOne: {
    fontSize: 14,
    fontWeight: 'bold',
    borderRightWidth: 1,
    padding: 7,
    margin: 'auto',
    width: 119.3,
    height: 29.82,
    backgroundColor: 'blue',
    borderTopLeftRadius: 50,
    borderBottomLeftRadius: 50,
    textAlign: 'center',
    color: 'white',
  },

  titleTwo: {
    fontSize: 14,
    fontWeight: 'bold',
    padding: 7,
    margin: 'auto',
    borderTopRightRadius: 50,
    borderBottomRightRadius: 50,
    textAlign: 'center',
    color: 'blue',
    backgroundColor: '#D9D9D9',
    width: 119.3,
    height: 29.82,
  },
  chartContainer: {
    padding: 10,
    paddingBottom: 100
  },
});

export default ChartProduct;
