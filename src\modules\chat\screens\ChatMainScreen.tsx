import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { ColorThemes } from '../../../assets/skin/colors';
import { navigate, RootScreen } from '../../../router/router';
import { useSocketStatus } from '../../../hooks/useSocketConnection';
import HomeHeader from '../../../Screen/Layout/headers/HomeHeader';
import ChatListScreen from './ChatListScreen';
import ContactsScreen from './ContactsScreen';
import CallHistoryScreen from './CallHistoryScreen';
import HeaderLogo from '../../../Screen/Layout/headers/HeaderLogo';

const ChatMainScreen: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'messages' | 'contacts' | 'calls'>('messages');
  const { isConnected, connectionStatus } = useSocketStatus();

  const tabs = [
    {
      id: 'messages',
      title: 'Tin nhắn',
      icon: '💬',
      badge: 21, // Số tin nhắn chưa đọc
    },
    {
      id: 'contacts',
      title: 'Danh bạ',
      icon: '👥',
    },
    {
      id: 'calls',
      title: 'Lịch sử cuộc gọi',
      icon: '📞',
    },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'messages':
        return <ChatListScreen />;
      case 'contacts':
        return <ContactsScreen />;
      case 'calls':
        return <CallHistoryScreen />;
      default:
        return <ChatListScreen />;
    }
  };

  const renderTabBar = () => (
    <View style={styles.tabContainer}>
      {/* Search bar */}
      <View style={styles.searchContainer}>
        <TouchableOpacity style={styles.searchBar} activeOpacity={0.8}>
          <Text style={styles.searchPlaceholder}>Bạn muốn tìm gì?</Text>
        </TouchableOpacity>
      </View>

      {/* Tab buttons */}
      <View style={styles.tabBar}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.id}
            style={[
              styles.tabButton,
              activeTab === tab.id && styles.activeTabButton,
            ]}
            onPress={() => setActiveTab(tab.id as any)}
            activeOpacity={0.7}
          >
            <View style={styles.tabContent}>
              <Text style={styles.tabIcon}>{tab.icon}</Text>
              <Text
                style={[
                  styles.tabText,
                  activeTab === tab.id && styles.activeTabText,
                ]}
              >
                {tab.title}
              </Text>
              {tab.badge && tab.badge > 0 && (
                <View style={styles.tabBadge}>
                  <Text style={styles.tabBadgeText}>
                    {tab.badge > 99 ? '99+' : tab.badge}
                  </Text>
                </View>
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>

      {/* Create group button */}
      <TouchableOpacity
        style={styles.createGroupButton}
        activeOpacity={0.7}
        onPress={() => navigate(RootScreen.CreateGroup)}
      >
        <Text style={styles.createGroupIcon}>+</Text>
        <Text style={styles.createGroupText}>Create group</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />
      
      {/* Header giống trang Home */}
      <HeaderLogo />
      
      {/* Tab navigation và content */}
      <View style={styles.content}>
        {renderTabBar()}

        {/* Socket connection status */}
        {!isConnected && (
          <View style={styles.connectionStatus}>
            <Text style={styles.connectionText}>
              {connectionStatus === 'disconnected' ? 'Đang kết nối...' : 'Mất kết nối'}
            </Text>
          </View>
        )}

        <View style={styles.tabContentContainer}>
          {renderTabContent()}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  content: {
    flex: 1,
    backgroundColor: 'white',
  },
  tabContainer: {
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingTop: 16,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_border_color,
  },
  searchContainer: {
    marginBottom: 16,
  },
  searchBar: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_border_color,
  },
  searchPlaceholder: {
    color: '#999',
    fontSize: 14,
  },
  tabBar: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
    position: 'relative',
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: ColorThemes.light.primary_color,
  },
  tabContent: {
    alignItems: 'center',
    position: 'relative',
  },
  tabIcon: {
    fontSize: 16,
    marginBottom: 4,
  },
  tabText: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_secondary_color,
    fontWeight: '500',
  },
  activeTabText: {
    color: ColorThemes.light.primary_color,
    fontWeight: '600',
  },
  tabBadge: {
    position: 'absolute',
    top: -8,
    right: -12,
    backgroundColor: ColorThemes.light.error_color,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  tabBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  createGroupButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    marginBottom: 8,
  },
  createGroupIcon: {
    fontSize: 18,
    marginRight: 8,
    color: ColorThemes.light.primary_color,
    fontWeight: 'bold',
  },
  createGroupText: {
    fontSize: 16,
    color: ColorThemes.light.neutral_text_color,
    fontWeight: '500',
  },
  tabContentContainer: {
    flex: 1,
  },
  connectionStatus: {
    backgroundColor: ColorThemes.light.warning_color,
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginHorizontal: 16,
    marginTop: 8,
    borderRadius: 4,
  },
  connectionText: {
    color: 'white',
    fontSize: 14,
    textAlign: 'center',
    fontWeight: '500',
  },
});

export default ChatMainScreen;
