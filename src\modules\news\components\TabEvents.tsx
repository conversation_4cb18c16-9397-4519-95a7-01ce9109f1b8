import React, {useEffect, useMemo, useCallback, useState} from 'react';
import {
  StyleSheet,
  View,
  ActivityIndicator,
  FlatList,
  ListRenderItem,
  ScrollView,
  Text,
  TouchableOpacity,
} from 'react-native';
import {SectionHeader} from './TabEventComponents/components/SectionHeader';
import {EventCardHorizontal, EventCardVertical} from '../card/EventCards';
import {useDispatch, useSelector} from 'react-redux';
import {RootState, AppDispatch} from '../../../redux/store/store';
import {NewsEvent} from '../../../redux/models/newsEvent';
import {ColorThemes} from '../../../assets/skin/colors';
import {
  fetchNewsEvents,
  loadMoreNewsEvent,
} from '../../../redux/actions/newEventAction';
import {isCurrentTimeInRange} from '../../../utils/Utils';
import {navigate} from '../../../router/router';
import {RootScreen} from '../../../router/router';
import {useNavigation} from '@react-navigation/native';

const TabEvents: React.FC = () => {
  const dispatch: AppDispatch = useDispatch();
  const {data, totalCount, loading, loadingMore} = useSelector(
    (state: RootState) => state.newsEvent,
  );
  const [page, setPage] = useState(1);
  const [ongoingEvents, setOngoingEvents] = useState<NewsEvent[]>([]);

  useEffect(() => {
    dispatch(fetchNewsEvents());
  }, [dispatch]);

  // lọc sự kiện đang diễn ra
  useEffect(() => {
    const ongoingEvents = data.filter(e =>
      isCurrentTimeInRange(e.DateStart, e.DateEnd),
    );
    setOngoingEvents(ongoingEvents);
  }, [data]);

  // load more sự kiện
  const handleLoadMore = () => {
    dispatch(loadMoreNewsEvent({page: page + 1}));
    setPage(page + 1);
  };

  // render sự kiện đang diễn ra
  const renderHorizontalItem: ListRenderItem<NewsEvent> = useCallback(
    ({item}) => <EventCardHorizontal item={item} />,
    [],
  );

  // render tất cả sự kiện
  const renderVerticalItem = useCallback(
    () => data.map(e => <EventCardVertical key={e.Id} item={e} />),
    [data],
  );

  // header sự kiện đang diễn ra
  const onGoingEvents = useMemo(
    () => (
      <View>
        <SectionHeader title="Đang diễn ra" showSeeMore={false} />
        <FlatList
          data={ongoingEvents}
          renderItem={renderHorizontalItem}
          keyExtractor={item => item.Id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.hList}
        />
      </View>
    ),
    [ongoingEvents, renderHorizontalItem],
  );

  // đang tải dữ liệu
  if (loading) {
    return (
      <View style={styles.center}>
        <ActivityIndicator
          size="large"
          color={ColorThemes.light.primary_main_color}
        />
      </View>
    );
  }

  // không có dữ liệu
  if (data && data.length === 0) {
    return (
      <View style={styles.center}>
        <Text>Hiện tại chưa có sự kiện nào</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {ongoingEvents && ongoingEvents.length > 0 && onGoingEvents}

      {/* All Events Section */}
      {data && data.length > 0 && (
        <View style={styles.containerVertical}>
          <SectionHeader title="Tất cả sự kiện" />
          <View style={styles.vList}>{renderVerticalItem()}</View>
          {data.length < totalCount && (
            <View style={styles.loadMoreButtonContainer}>
              <TouchableOpacity
                style={styles.loadMoreButton}
                onPress={handleLoadMore}
                disabled={loadingMore}>
                {loadingMore ? (
                  <ActivityIndicator color="#FFFFFF" />
                ) : (
                  <Text style={styles.loadMoreButtonText}>Xem thêm</Text>
                )}
              </TouchableOpacity>
            </View>
          )}
        </View>
      )}
    </ScrollView>
  );
};

export default TabEvents;

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    paddingBottom: 16,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  center: {flex: 1, justifyContent: 'center', alignItems: 'center'},
  hList: {paddingLeft: 16, paddingRight: 8, paddingBottom: 16},
  vList: {paddingHorizontal: 16},
  loadMoreButtonContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 10,
  },
  loadMoreButton: {
    height: 35,
    width: 100,
    backgroundColor: ColorThemes.light.primary_main_color,
    padding: 6,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
  containerVertical: {
    flex: 1,
    paddingBottom: 20,
  },
  loadMoreButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 12,
  },
});
